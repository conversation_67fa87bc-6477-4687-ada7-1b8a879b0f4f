"""Comprehensive Exploration Strategy for Turret Optimization"""

import time
import json
from typing import Dict, List, Any
from dataclasses import dataclass
from loguru import logger as log

from parallel_experiments import ParallelExperimentManager, BatchResult


@dataclass
class ExplorationConfig:
    """Configuration for exploration strategy"""

    # Experiment durations
    quick_duration: float = 15.0  # For rapid exploration
    standard_duration: float = 30.0  # For detailed evaluation
    long_duration: float = 60.0  # For final validation

    # Batch sizes
    random_search_size: int = 16
    grid_search_steps: int = 3
    genetic_population: int = 20
    genetic_generations: int = 3
    exploitation_variations: int = 8

    # Strategy weights
    exploration_weight: float = 0.7  # How much to explore vs exploit
    convergence_threshold: float = 0.05  # KPI improvement threshold

    # Resource limits
    max_parallel_workers: int = 6
    max_total_experiments: int = 200


class ExplorationStrategy:
    """Comprehensive exploration strategy manager"""

    def __init__(self, config: ExplorationConfig = None):
        self.config = config or ExplorationConfig()
        self.manager = ParallelExperimentManager(
            max_workers=self.config.max_parallel_workers
        )
        self.exploration_history: List[BatchResult] = []
        self.best_configs: List[Dict[str, Any]] = []
        self.convergence_history: List[float] = []

        log.info(
            f"Exploration Strategy initialized with {self.config.max_parallel_workers} workers"
        )

    def run_comprehensive_exploration(self) -> Dict[str, Any]:
        """Run a comprehensive exploration strategy"""
        log.info("Starting comprehensive exploration strategy...")
        start_time = time.time()

        # Phase 1: Broad Random Exploration
        log.info("Phase 1: Broad Random Exploration")
        random_results = self._run_random_exploration()
        self._update_best_configs(random_results)

        # Phase 2: Grid Search in Promising Regions
        log.info("Phase 2: Grid Search Validation")
        grid_results = self._run_grid_exploration()
        self._update_best_configs(grid_results)

        # Phase 3: Genetic Algorithm Optimization
        log.info("Phase 3: Genetic Algorithm Optimization")
        genetic_results = self._run_genetic_exploration()
        for batch in genetic_results:
            self._update_best_configs(batch)

        # Phase 4: Exploitation Around Best Configs
        log.info("Phase 4: Exploitation Around Best Configurations")
        exploitation_results = self._run_exploitation_phase()
        self._update_best_configs(exploitation_results)

        # Phase 5: Final Validation
        log.info("Phase 5: Final Validation of Top Configurations")
        validation_results = self._run_validation_phase()
        self._update_best_configs(validation_results)

        # Generate comprehensive report
        total_time = time.time() - start_time
        return self._generate_exploration_report(total_time)

    def _run_random_exploration(self) -> BatchResult:
        """Phase 1: Broad random exploration"""
        return self.manager.run_random_search(
            num_experiments=self.config.random_search_size,
            duration=self.config.quick_duration,
        )

    def _run_grid_exploration(self) -> BatchResult:
        """Phase 2: Grid search for systematic exploration"""
        return self.manager.run_grid_search(
            steps_per_param=self.config.grid_search_steps,
            duration=self.config.quick_duration,
        )

    def _run_genetic_exploration(self) -> List[BatchResult]:
        """Phase 3: Genetic algorithm optimization"""
        return self.manager.run_genetic_optimization(
            population_size=self.config.genetic_population,
            generations=self.config.genetic_generations,
            duration=self.config.standard_duration,
        )

    def _run_exploitation_phase(self) -> BatchResult:
        """Phase 4: Exploit around best configurations"""
        if not self.best_configs:
            log.warning("No best configs found for exploitation phase")
            return self.manager.run_random_search(
                num_experiments=4, duration=self.config.standard_duration
            )

        # Take top 3 configs for exploitation
        top_configs = self.best_configs[:3]
        return self.manager.run_exploitation_search(
            base_configs=top_configs,
            variations_per_config=self.config.exploitation_variations,
            duration=self.config.standard_duration,
        )

    def _run_validation_phase(self) -> BatchResult:
        """Phase 5: Final validation with longer experiments"""
        if not self.best_configs:
            log.warning("No configs to validate")
            return self.manager.run_random_search(
                num_experiments=2, duration=self.config.long_duration
            )

        # Validate top 5 configs with longer experiments
        validation_configs = self.best_configs[:5]

        from parallel_experiments import ExperimentBatch

        batch = ExperimentBatch(
            batch_id=f"validation_{int(time.time())}",
            configs=validation_configs,
            duration=self.config.long_duration,
        )

        return self.manager._run_batch(batch)

    def _update_best_configs(self, batch_result: BatchResult):
        """Update best configurations from batch results"""
        self.exploration_history.append(batch_result)

        # Add new configs to best list
        for exp in batch_result.experiments:
            if exp.kpi_score > 0.3:  # Minimum threshold for "good" configs
                self.best_configs.append(exp.config_params)

        # Sort by KPI and keep top configs
        all_experiments = []
        for batch in self.exploration_history:
            all_experiments.extend(batch.experiments)

        all_experiments.sort(key=lambda x: x.kpi_score, reverse=True)
        self.best_configs = [exp.config_params for exp in all_experiments[:20]]

        # Track convergence
        if len(all_experiments) > 0:
            current_best_kpi = all_experiments[0].kpi_score
            self.convergence_history.append(current_best_kpi)

        log.info(f"Updated best configs. Current best KPI: {batch_result.best_kpi:.3f}")

    def _check_convergence(self) -> bool:
        """Check if optimization has converged"""
        if len(self.convergence_history) < 3:
            return False

        recent_improvement = self.convergence_history[-1] - self.convergence_history[-3]

        return recent_improvement < self.config.convergence_threshold

    def _generate_exploration_report(self, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive exploration report"""
        all_experiments = []
        for batch in self.exploration_history:
            all_experiments.extend(batch.experiments)

        if not all_experiments:
            return {"error": "No experiments completed"}

        all_experiments.sort(key=lambda x: x.kpi_score, reverse=True)

        report = {
            "exploration_summary": {
                "total_time_minutes": total_time / 60,
                "total_experiments": len(all_experiments),
                "total_batches": len(self.exploration_history),
                "best_kpi": all_experiments[0].kpi_score,
                "avg_kpi": sum(exp.kpi_score for exp in all_experiments)
                / len(all_experiments),
                "convergence_achieved": self._check_convergence(),
            },
            "best_configurations": [
                {
                    "rank": i + 1,
                    "kpi_score": exp.kpi_score,
                    "accuracy": exp.accuracy,
                    "hits_per_minute": exp.hits_per_minute,
                    "config": exp.config_params,
                }
                for i, exp in enumerate(all_experiments[:10])
            ],
            "phase_results": [
                {
                    "batch_id": batch.batch_id,
                    "experiments": len(batch.experiments),
                    "best_kpi": batch.best_kpi,
                    "avg_kpi": batch.avg_kpi,
                    "duration_minutes": batch.duration / 60,
                }
                for batch in self.exploration_history
            ],
            "convergence_history": self.convergence_history,
            "recommendations": self._generate_recommendations(all_experiments),
        }

        # Save detailed results
        filename = f"exploration_report_{int(time.time())}.json"
        with open(filename, "w") as f:
            json.dump(report, f, indent=2)

        log.info(f"Exploration report saved to {filename}")
        return report

    def _generate_recommendations(self, experiments: List) -> Dict[str, Any]:
        """Generate optimization recommendations"""
        if not experiments:
            return {"error": "No experiments to analyze"}

        best_exp = experiments[0]

        recommendations = {
            "optimal_config": best_exp.config_params,
            "expected_performance": {
                "kpi_score": best_exp.kpi_score,
                "accuracy": best_exp.accuracy,
                "hits_per_minute": best_exp.hits_per_minute,
            },
            "key_insights": [],
            "parameter_analysis": {},
        }

        # Analyze parameter trends
        top_10 = experiments[:10]

        for param in [
            "fire_cooldown",
            "fire_cooldown_close",
            "angle_threshold_close",
            "angle_threshold_far",
            "max_engagement_distance",
        ]:
            values = [exp.config_params.get(param, 0) for exp in top_10]
            if values:
                recommendations["parameter_analysis"][param] = {
                    "optimal_range": [min(values), max(values)],
                    "best_value": best_exp.config_params.get(param, 0),
                    "avg_top_10": sum(values) / len(values),
                }

        # Generate insights
        if best_exp.accuracy > 80:
            recommendations["key_insights"].append(
                "High accuracy achieved - focus on fire rate optimization"
            )
        if best_exp.hits_per_minute > 25:
            recommendations["key_insights"].append(
                "Excellent hit rate - configuration is well-tuned"
            )
        if best_exp.kpi_score > 0.7:
            recommendations["key_insights"].append(
                "Outstanding overall performance - ready for deployment"
            )

        return recommendations


def run_exploration_strategy():
    """Main function to run exploration strategy"""
    config = ExplorationConfig(
        quick_duration=10.0,  # Faster for demo
        standard_duration=20.0,
        random_search_size=12,
        genetic_population=16,
        genetic_generations=2,
        max_parallel_workers=4,
    )

    strategy = ExplorationStrategy(config)
    report = strategy.run_comprehensive_exploration()

    # Print summary
    print("\n" + "=" * 60)
    print("EXPLORATION STRATEGY COMPLETE")
    print("=" * 60)

    summary = report["exploration_summary"]
    print(f"Total Time: {summary['total_time_minutes']:.1f} minutes")
    print(f"Total Experiments: {summary['total_experiments']}")
    print(f"Best KPI: {summary['best_kpi']:.3f}")
    print(f"Average KPI: {summary['avg_kpi']:.3f}")

    print("\nTop 3 Configurations:")
    for config in report["best_configurations"][:3]:
        print(
            f"{config['rank']}. KPI: {config['kpi_score']:.3f}, "
            f"Accuracy: {config['accuracy']:.1f}%, "
            f"HPM: {config['hits_per_minute']:.1f}"
        )

    print("\nRecommended Configuration:")
    rec = report["recommendations"]
    print(f"Expected KPI: {rec['expected_performance']['kpi_score']:.3f}")
    print(f"Expected Accuracy: {rec['expected_performance']['accuracy']:.1f}%")
    print(f"Expected HPM: {rec['expected_performance']['hits_per_minute']:.1f}")

    return report


if __name__ == "__main__":
    run_exploration_strategy()
