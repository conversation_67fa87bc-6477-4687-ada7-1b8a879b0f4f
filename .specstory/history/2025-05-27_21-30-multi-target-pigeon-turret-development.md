<!-- Generated by SpecStory -->

# Multi-Target Pigeon Turret Development (2025-05-27 22:30:06)

_**User**_

@Automated Pigeon Turret Simulation Development 
@Pigeon Turret Experimentation Strategy 

--

MULTI TARGET UPGRADE!!!

---

OK - how about we make a new file for making a multi-target path?
new config: multi_target_max = 10
The multi-target picks n targets and plans 10 shots, then actions them. This is trick so document the issues and solutions well (eg estimating time-to-move-turret as those will add up per movement. Path optimization, could use eg pathfinding algo, will it run fast enough though? algo needs to run FAST as the objects it's tracking move fast.

Think it through first then output a mini-PRD, save to dir called specs.
Then make the feature!

---

_**Assistant**_

I'll analyze the multi-target upgrade requirements and create a comprehensive PRD before implementing the feature.

---

