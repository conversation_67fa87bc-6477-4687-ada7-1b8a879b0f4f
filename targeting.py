"""Targeting algorithms for automated turret control"""

import numpy as np
import math
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger as log
import time

from entities import Pigeon, EntityManager
from camera import Camera


@dataclass
class TargetingDecision:
    """Result of targeting algorithm"""

    target_pigeon: Optional[Pigeon]
    target_yaw: float
    target_pitch: float
    confidence: float  # 0-1 score
    estimated_time_to_target: float
    algorithm_name: str


class TargetingAlgorithm(ABC):
    """Base class for targeting algorithms"""

    def __init__(self, name: str):
        self.name = name
        self.shots_fired = 0
        self.hits = 0

    @abstractmethod
    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        """Select best target from available pigeons"""
        pass

    def update_stats(self, hit: bool):
        """Update algorithm statistics"""
        self.shots_fired += 1
        if hit:
            self.hits += 1

    @property
    def accuracy(self) -> float:
        """Get current accuracy"""
        return self.hits / self.shots_fired if self.shots_fired > 0 else 0.0


class NearestTargeting(TargetingAlgorithm):
    """Simple nearest target selection"""

    def __init__(self):
        super().__init__("Nearest")

    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        if not pigeons:
            return None

        # Find nearest pigeon
        nearest_pigeon = None
        min_distance = float("inf")

        for pigeon in pigeons:
            if pigeon.active:
                distance = np.linalg.norm(pigeon.position)
                if distance < min_distance:
                    min_distance = distance
                    nearest_pigeon = pigeon

        if not nearest_pigeon:
            return None

        yaw, pitch, _ = nearest_pigeon.to_spherical()
        time_to_target = camera.get_movement_time(yaw, pitch)

        return TargetingDecision(
            target_pigeon=nearest_pigeon,
            target_yaw=yaw,
            target_pitch=pitch,
            confidence=1.0
            / (1.0 + min_distance / 1000),  # Higher confidence for closer targets
            estimated_time_to_target=time_to_target,
            algorithm_name=self.name,
        )


class PredictiveTargeting(TargetingAlgorithm):
    """Predictive targeting with motion compensation"""

    def __init__(self):
        super().__init__("Predictive")

    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        if not pigeons:
            return None

        best_score = -float("inf")
        best_decision = None

        # Sort pigeons by distance for prioritization
        sorted_pigeons = sorted(pigeons, key=lambda p: np.linalg.norm(p.position))

        for pigeon in sorted_pigeons:
            if not pigeon.active:
                continue

            # Calculate predicted position after turret movement
            yaw, pitch, distance = pigeon.to_spherical()

            # Skip targets that are too far
            if distance > 1200:  # MAX_ENGAGEMENT_DISTANCE
                continue

            movement_time = camera.get_movement_time(yaw, pitch)

            # Predict pigeon position after movement time
            future_pos = pigeon.position + pigeon.velocity * movement_time
            future_yaw, future_pitch, future_distance = self._to_spherical(future_pos)

            # Calculate projectile travel time
            projectile_time = future_distance / 500.0  # Projectile speed

            # Limit prediction time to avoid over-prediction
            total_time = min(movement_time + projectile_time, 2.0)

            # Final predicted position
            final_pos = pigeon.position + pigeon.velocity * total_time
            final_yaw, final_pitch, final_distance = self._to_spherical(final_pos)

            # Enhanced scoring system
            # 1. Distance score (heavily favor close targets)
            distance_score = 1.0 / (
                1.0 + final_distance / 400
            )  # More aggressive than before

            # 2. Speed penalty (slower targets are easier)
            speed = np.linalg.norm(pigeon.velocity)
            speed_score = 1.0 / (1.0 + speed / 80)  # Adjusted for new speed ranges

            # 3. Angular velocity penalty
            angular_vel = self._calculate_angular_velocity(pigeon, camera)
            velocity_score = 1.0 / (1.0 + angular_vel / 25)

            # 4. Time penalty (prefer quicker shots)
            time_score = 1.0 / (1.0 + movement_time)

            # 5. Shot history penalty (prefer fresh targets)
            shot_penalty = 1.0 / (1.0 + pigeon.shots_taken_at * 0.3)

            # Combined score with prioritization
            score = (
                distance_score
                * 3.0  # Heavy distance weight
                * speed_score
                * 1.5  # Moderate speed weight
                * velocity_score
                * 1.2  # Angular velocity weight
                * time_score
                * 1.0  # Time weight
                * shot_penalty
                * 1.5
            )  # Fresh target bonus

            if score > best_score:
                best_score = score
                best_decision = TargetingDecision(
                    target_pigeon=pigeon,
                    target_yaw=final_yaw,
                    target_pitch=final_pitch,
                    confidence=min(score / 5.0, 1.0),  # Normalize confidence
                    estimated_time_to_target=movement_time,
                    algorithm_name=self.name,
                )

        return best_decision

    def _to_spherical(self, position: np.ndarray) -> Tuple[float, float, float]:
        """Convert 3D position to spherical coordinates"""
        x, y, z = position
        distance = np.linalg.norm(position)

        if distance < 0.001:
            return 0, 0, 0

        yaw = math.degrees(math.atan2(x, z)) % 360
        pitch = math.degrees(math.asin(np.clip(y / distance, -1, 1)))

        return yaw, pitch, distance

    def _calculate_angular_velocity(self, pigeon: Pigeon, camera: Camera) -> float:
        """Calculate angular velocity of pigeon relative to camera"""
        # Current angles
        yaw1, pitch1, dist1 = pigeon.to_spherical()

        # Future position (small time step)
        dt = 0.1
        future_pos = pigeon.position + pigeon.velocity * dt
        yaw2, pitch2, dist2 = self._to_spherical(future_pos)

        # Angular differences
        yaw_diff = abs((yaw2 - yaw1 + 180) % 360 - 180)
        pitch_diff = abs(pitch2 - pitch1)

        # Angular velocity in degrees per second
        angular_vel = math.sqrt(yaw_diff**2 + pitch_diff**2) / dt

        return angular_vel


class OpportunisticTargeting(TargetingAlgorithm):
    """Target pigeons that are easiest to hit right now"""

    def __init__(self):
        super().__init__("Opportunistic")

    def select_target(
        self, pigeons: List[Pigeon], camera: Camera, entity_manager: EntityManager
    ) -> Optional[TargetingDecision]:
        if not pigeons:
            return None

        best_score = -float("inf")
        best_decision = None

        for pigeon in pigeons:
            if not pigeon.active:
                continue

            yaw, pitch, distance = pigeon.to_spherical()

            # Skip targets that are too far
            if distance > 1000:  # Even stricter for opportunistic
                continue

            # Calculate how close pigeon is to current camera position
            angle_diff_yaw = abs((yaw - camera.state.yaw + 180) % 360 - 180)
            angle_diff_pitch = abs(pitch - camera.state.pitch)
            total_angle_diff = math.sqrt(angle_diff_yaw**2 + angle_diff_pitch**2)

            # Score based on minimal movement needed (heavily weighted)
            movement_score = 1.0 / (
                1.0 + total_angle_diff / 5
            )  # More sensitive to angle

            # Distance score (prefer very close targets)
            distance_score = 1.0 / (
                1.0 + distance / 300
            )  # More aggressive distance preference

            # Prefer slow-moving targets
            speed = np.linalg.norm(pigeon.velocity)
            speed_score = 1.0 / (1.0 + speed / 100)

            # Combined score with heavy weight on movement efficiency
            score = movement_score * 3.0 * distance_score * 2.0 * speed_score

            if score > best_score:
                best_score = score

                # For opportunistic, minimal lead compensation for close targets
                if distance < 500:
                    lead_time = distance / 500.0 * 0.3  # Less lead for close targets
                else:
                    lead_time = distance / 500.0 * 0.5

                future_pos = pigeon.position + pigeon.velocity * lead_time
                future_yaw, future_pitch, _ = self._to_spherical(future_pos)

                best_decision = TargetingDecision(
                    target_pigeon=pigeon,
                    target_yaw=future_yaw,
                    target_pitch=future_pitch,
                    confidence=min(score, 1.0),
                    estimated_time_to_target=camera.get_movement_time(
                        future_yaw, future_pitch
                    ),
                    algorithm_name=self.name,
                )

        return best_decision

    def _to_spherical(self, position: np.ndarray) -> Tuple[float, float, float]:
        """Convert 3D position to spherical coordinates"""
        x, y, z = position
        distance = np.linalg.norm(position)

        if distance < 0.001:
            return 0, 0, 0

        yaw = math.degrees(math.atan2(x, z)) % 360
        pitch = math.degrees(math.asin(np.clip(y / distance, -1, 1)))

        return yaw, pitch, distance


class TargetingSystem:
    """Main targeting system that can switch between algorithms"""

    def __init__(self, initial_algorithm: str = "predictive"):
        self.algorithms = {
            "nearest": NearestTargeting(),
            "predictive": PredictiveTargeting(),
            "opportunistic": OpportunisticTargeting(),
        }
        self.current_algorithm = self.algorithms.get(
            initial_algorithm, self.algorithms["predictive"]
        )
        self.last_fire_time = 0.0
        self.current_target_id: Optional[int] = None
        self.target_lock_time: float = 0.0
        self.min_target_persistence: float = (
            1.5  # Stick with target for at least 1.5 seconds
        )
        self.shot_times: List[float] = []  # Track recent shot times for rate limiting

    def select_target(
        self, entity_manager: EntityManager, camera: Camera
    ) -> Optional[TargetingDecision]:
        """Select target using current algorithm"""
        visible_pigeons = entity_manager.get_visible_pigeons(camera)

        # Filter out pigeons that have reached shot limit or are fleeing
        from config import CONFIG

        eligible_pigeons = []
        for pigeon in visible_pigeons:
            if (
                pigeon.active
                and not pigeon.is_fleeing
                and entity_manager.get_shots_at_pigeon(pigeon.id)
                < CONFIG.targeting.MAX_SHOTS_PER_TARGET
            ):
                eligible_pigeons.append(pigeon)

        # Check if we should stick with current target
        if self.current_target_id is not None:
            current_time = time.time()
            if current_time - self.target_lock_time < self.min_target_persistence:
                # Try to find current target in eligible pigeons
                for pigeon in eligible_pigeons:
                    if pigeon.id == self.current_target_id:
                        # Re-evaluate the current target
                        decision = self.current_algorithm.select_target(
                            [pigeon], camera, entity_manager
                        )
                        if decision:
                            return decision
                        break

        # Select new target from eligible pigeons
        decision = self.current_algorithm.select_target(
            eligible_pigeons, camera, entity_manager
        )

        # Update target lock if we have a new target
        if decision and decision.target_pigeon:
            if decision.target_pigeon.id != self.current_target_id:
                self.current_target_id = decision.target_pigeon.id
                self.target_lock_time = time.time()

        return decision

    def switch_algorithm(self, algorithm_name: str):
        """Switch to different targeting algorithm"""
        if algorithm_name in self.algorithms:
            self.current_algorithm = self.algorithms[algorithm_name]
            log.info(f"Switched to {algorithm_name} targeting algorithm")

    def can_fire(self, current_time: float, cooldown: float) -> bool:
        """Check if turret can fire based on cooldown and rate limiting"""
        from config import CONFIG

        # Basic cooldown check
        if current_time - self.last_fire_time < cooldown:
            return False

        # Rate limiting check
        self._cleanup_old_shots(current_time)

        # Check if we're under the shots per second limit
        if len(self.shot_times) >= CONFIG.targeting.MAX_SHOTS_PER_SECOND:
            return False

        return True

    def _cleanup_old_shots(self, current_time: float):
        """Remove shot times older than 1 second"""
        self.shot_times = [t for t in self.shot_times if current_time - t < 1.0]

    def fire(self, current_time: float):
        """Record fire event"""
        self.last_fire_time = current_time
        self.shot_times.append(current_time)

    def get_stats(self) -> dict:
        """Get statistics for all algorithms"""
        return {
            name: {
                "shots": algo.shots_fired,
                "hits": algo.hits,
                "accuracy": algo.accuracy,
            }
            for name, algo in self.algorithms.items()
        }
