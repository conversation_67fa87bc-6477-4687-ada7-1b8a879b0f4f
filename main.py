"""Pigeon Turret Simulator - Main Application"""

import pygame
import sys
import time
import numpy as np
from loguru import logger as log

# Configure logger
log.remove()  # Remove default handler
log.add(
    sys.stderr,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | <level>{message}</level>",
    level="INFO",
    colorize=True,
)

from config import CONFIG
from camera import Camera
from entities import EntityManager, Pigeon
from environment import Environment
from targeting import TargetingSystem
from evaluation import PerformanceEvaluator, ShotMetrics
from brain import AIBrain


class TurretSimulator:
    """Main turret simulation application"""

    def __init__(self):
        pygame.init()

        # Window setup
        self.window_size = (
            CONFIG.environment.WINDOW_WIDTH,
            CONFIG.environment.WINDOW_HEIGHT,
        )
        self.screen = pygame.display.set_mode(self.window_size)
        pygame.display.set_caption("Pigeon Turret Simulator")

        # Clock for FPS control
        self.clock = pygame.time.Clock()
        self.running = True
        self.paused = False
        self.show_debug = CONFIG.environment.DEBUG_MODE

        # Initialize components
        self.camera = Camera(self.window_size, CONFIG.environment.CAMERA_FOV)
        self.camera.max_speed = CONFIG.environment.TURRET_MAX_SPEED
        self.camera.acceleration = CONFIG.environment.TURRET_ACCELERATION

        self.entity_manager = EntityManager()
        self.environment = Environment(self.window_size)
        self.targeting_system = TargetingSystem(CONFIG.targeting.TARGETING_MODE)
        self.evaluator = PerformanceEvaluator()

        # Initialize AI Brain
        self.ai_brain = AIBrain(self)

        # Load panorama if available
        self.environment.load_panorama(CONFIG.environment.PANORAMA_PATH)

        # Control states
        self.manual_control = False
        self.auto_fire = True
        self.current_algorithm = CONFIG.targeting.TARGETING_MODE

        # Burst fire state
        self.burst_queue = []  # List of (pigeon, fire_time) for burst shots

        # Turret search behavior
        self.last_target_time = 0.0  # Last time we had a target
        self.search_active = False
        self.search_start_time = 0.0
        self.current_search_angle = CONFIG.environment.TURRET_SEARCH_ANGLE

        # Timing
        self.start_time = time.time()
        self.last_report_time = self.start_time

        log.info("Turret Simulator initialized")

    def handle_events(self):
        """Handle user input events"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False

            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False

                elif event.key == pygame.K_SPACE:
                    self.paused = not self.paused
                    log.info(f"Simulation {'paused' if self.paused else 'resumed'}")

                elif event.key == pygame.K_d:
                    self.show_debug = not self.show_debug

                elif event.key == pygame.K_m:
                    self.manual_control = not self.manual_control
                    log.info(
                        f"Manual control: {'ON' if self.manual_control else 'OFF'}"
                    )

                elif event.key == pygame.K_a:
                    self.auto_fire = not self.auto_fire
                    log.info(f"Auto fire: {'ON' if self.auto_fire else 'OFF'}")

                elif event.key == pygame.K_f and self.manual_control:
                    self._manual_fire()

                elif event.key == pygame.K_b:
                    # Toggle AI Brain
                    CONFIG.brain.BRAIN_ENABLED = not CONFIG.brain.BRAIN_ENABLED
                    if CONFIG.brain.BRAIN_ENABLED:
                        self.ai_brain.start()
                        log.info("AI Brain enabled")
                    else:
                        self.ai_brain.stop()
                        log.info("AI Brain disabled")

                # Algorithm switching
                elif event.key == pygame.K_1:
                    self._switch_algorithm("nearest")
                elif event.key == pygame.K_2:
                    self._switch_algorithm("predictive")
                elif event.key == pygame.K_3:
                    self._switch_algorithm("opportunistic")

                # Manual camera control
                elif self.manual_control:
                    if event.key == pygame.K_LEFT:
                        self.camera.target_state.yaw -= 45
                    elif event.key == pygame.K_RIGHT:
                        self.camera.target_state.yaw += 45
                    elif event.key == pygame.K_UP:
                        self.camera.target_state.pitch += 30
                    elif event.key == pygame.K_DOWN:
                        self.camera.target_state.pitch -= 30

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    # Click to point camera
                    self.camera.set_target_from_screen(event.pos)
                    if self.manual_control:
                        self._manual_fire()

    def _switch_algorithm(self, algorithm_name: str):
        """Switch targeting algorithm"""
        self.targeting_system.switch_algorithm(algorithm_name)
        self.current_algorithm = algorithm_name
        CONFIG.targeting.TARGETING_MODE = algorithm_name
        log.info(f"Switched to {algorithm_name} targeting")

    def _manual_fire(self):
        """Fire manually at current camera position"""
        current_time = time.time() - self.start_time

        # Find pigeon closest to center of view
        best_pigeon = None
        min_angle_diff = float("inf")

        for pigeon in self.entity_manager.pigeons:
            if pigeon.active:
                yaw, pitch, distance = pigeon.to_spherical()
                angle_diff = abs(yaw - self.camera.state.yaw) + abs(
                    pitch - self.camera.state.pitch
                )
                if angle_diff < min_angle_diff:
                    min_angle_diff = angle_diff
                    best_pigeon = pigeon

        if best_pigeon and self.targeting_system.can_fire(
            current_time, CONFIG.targeting.FIRE_COOLDOWN
        ):
            self._fire_at_target(best_pigeon, current_time)

    def _fire_at_target(self, pigeon: Pigeon, current_time: float):
        """Fire projectile at target pigeon"""
        # Record shot attempt for this pigeon
        self.entity_manager.record_shot_at_pigeon(pigeon.id)

        projectile = self.entity_manager.fire_projectile(
            current_time, pigeon, CONFIG, mode=self.current_algorithm
        )

        if projectile:
            self.targeting_system.fire(current_time)

            # Calculate metrics
            yaw, pitch, distance = pigeon.to_spherical()
            movement_time = self.camera.get_movement_time(yaw, pitch)
            projectile_time = distance / CONFIG.targeting.PROJECTILE_SPEED

            # Create shot metrics (will update hit status later)
            shot_metrics = ShotMetrics(
                shot_time=current_time,
                target_id=pigeon.id,
                algorithm=self.current_algorithm,
                movement_time=movement_time,
                projectile_flight_time=projectile_time,
                hit=False,  # Will be updated when collision detected
                distance=distance,
                target_speed=np.linalg.norm(pigeon.velocity),
                angular_velocity=0.0,  # TODO: Calculate
            )

            # Record the shot immediately
            self.evaluator.record_shot(shot_metrics)

            # Store reference to update hit status later
            projectile._target_pigeon_id = pigeon.id
            projectile._evaluator = self.evaluator
            projectile._algorithm = self.current_algorithm

    def update(self, dt: float):
        """Update simulation state"""
        if self.paused:
            return

        current_time = time.time() - self.start_time

        # Update camera
        self.camera.update(dt)

        # Update entities
        self.entity_manager.update(dt, current_time, CONFIG)

        # Track new pigeons
        for pigeon in self.entity_manager.pigeons:
            if pigeon.id not in self.evaluator.current_targets:
                self.evaluator.record_pigeon_spawn(pigeon.id, current_time)

        # Process burst fire queue
        self._process_burst_fire(current_time)

        # Auto-targeting and firing
        target_found = False
        if self.auto_fire and not self.manual_control:
            decision = self.targeting_system.select_target(
                self.entity_manager, self.camera
            )

            if decision:
                target_found = True
                self.last_target_time = current_time
                self.search_active = False  # Stop searching when target found

                # Set camera target
                self.camera.set_target_angles(
                    decision.target_yaw, decision.target_pitch
                )

                # Calculate dynamic angle threshold based on distance
                if decision.target_pigeon:
                    _, _, distance = decision.target_pigeon.to_spherical()
                    if distance < 600:
                        angle_threshold = CONFIG.targeting.ANGLE_THRESHOLD_CLOSE
                    else:
                        # Interpolate between close and far thresholds
                        t = min((distance - 600) / 600, 1.0)
                        angle_threshold = CONFIG.targeting.ANGLE_THRESHOLD_CLOSE + t * (
                            CONFIG.targeting.ANGLE_THRESHOLD_FAR
                            - CONFIG.targeting.ANGLE_THRESHOLD_CLOSE
                        )
                else:
                    angle_threshold = CONFIG.targeting.ANGLE_THRESHOLD_FAR

                # Fire if ready and aimed
                angle_diff = abs(self.camera.state.yaw - decision.target_yaw) + abs(
                    self.camera.state.pitch - decision.target_pitch
                )

                # Adaptive fire cooldown based on distance
                if distance < CONFIG.targeting.CLOSE_TARGET_DISTANCE:
                    fire_cooldown = CONFIG.targeting.FIRE_COOLDOWN_CLOSE
                else:
                    fire_cooldown = CONFIG.targeting.FIRE_COOLDOWN

                if angle_diff < angle_threshold and self.targeting_system.can_fire(
                    current_time, fire_cooldown
                ):
                    # Check if target is close enough and slow enough for burst fire
                    target_speed = np.linalg.norm(decision.target_pigeon.velocity)
                    if (
                        distance < CONFIG.targeting.BURST_FIRE_DISTANCE
                        and target_speed < CONFIG.targeting.BURST_FIRE_MAX_SPEED
                    ):
                        self._initiate_burst_fire(decision.target_pigeon, current_time)
                    else:
                        self._fire_at_target(decision.target_pigeon, current_time)

        # Handle turret search behavior when no targets
        if not target_found and CONFIG.environment.TURRET_SEARCH_ENABLED:
            self._handle_search_behavior(current_time)

        # Metrics are now updated directly in collision detection

        # Periodic report
        if current_time - self.last_report_time > 10.0:
            self.last_report_time = current_time
            stats = self.evaluator.get_current_stats()
            brain_metrics = self.entity_manager.get_brain_metrics()
            log.info(
                f"Performance: {stats['session']['total_pigeons_hit']}/{stats['session']['total_pigeons_spawned']} pigeons hit | "
                f"Escaped: {brain_metrics['escaped_targets']} | Active: {brain_metrics['active_pigeons']}"
            )

    def _initiate_burst_fire(self, pigeon: Pigeon, current_time: float):
        """Initiate burst fire sequence for close target"""
        # Fire first shot immediately
        self._fire_at_target(pigeon, current_time)

        # Queue additional burst shots
        for i in range(1, CONFIG.targeting.BURST_FIRE_COUNT + 1):
            burst_time = current_time + (i * CONFIG.targeting.BURST_FIRE_INTERVAL)
            self.burst_queue.append((pigeon, burst_time))

    def _process_burst_fire(self, current_time: float):
        """Process queued burst fire shots"""
        shots_to_fire = []
        remaining_queue = []

        for pigeon, fire_time in self.burst_queue:
            if current_time >= fire_time:
                # Check if pigeon is still valid target
                if pigeon.active and not pigeon.is_fleeing:
                    shots_to_fire.append(pigeon)
            else:
                remaining_queue.append((pigeon, fire_time))

        # Fire queued shots
        for pigeon in shots_to_fire:
            if self.targeting_system.can_fire(
                current_time, 0.1
            ):  # Reduced cooldown for burst
                self._fire_at_target(pigeon, current_time)

        # Update queue
        self.burst_queue = remaining_queue

    def _handle_search_behavior(self, current_time: float):
        """Handle automatic turret search when no targets available"""
        time_since_target = current_time - self.last_target_time

        if time_since_target >= CONFIG.environment.TURRET_SEARCH_TIMEOUT:
            if not self.search_active:
                # Start searching
                self.search_active = True
                self.search_start_time = current_time

                # Determine search angle based on how long we've been without targets
                total_no_target_time = time_since_target
                if (
                    total_no_target_time
                    >= CONFIG.environment.TURRET_SEARCH_REDUCE_TIMEOUT
                ):
                    self.current_search_angle = (
                        CONFIG.environment.TURRET_SEARCH_REDUCED_ANGLE
                    )
                    log.debug(
                        f"Turret search: Using reduced angle {self.current_search_angle}°"
                    )
                else:
                    self.current_search_angle = CONFIG.environment.TURRET_SEARCH_ANGLE
                    log.debug(
                        f"Turret search: Using full angle {self.current_search_angle}°"
                    )

                # Start search by turning
                target_yaw = (self.camera.state.yaw + self.current_search_angle) % 360
                self.camera.set_target_angles(target_yaw, self.camera.state.pitch)

                if CONFIG.brain.LOG_MOVEMENT_PATTERNS:
                    log.info(
                        f"Turret search initiated: {self.current_search_angle}° turn (no targets for {time_since_target:.1f}s)"
                    )

    def render(self):
        """Render the simulation"""
        self.environment.render(
            self.screen, self.camera, self.entity_manager, self.show_debug
        )

        # Render UI overlay
        self._render_ui()

        pygame.display.flip()

    def _render_ui(self):
        """Render UI overlay"""
        font = pygame.font.Font(None, 24)
        ui_lines = []

        # Status line
        status = (
            "PAUSED" if self.paused else ("MANUAL" if self.manual_control else "AUTO")
        )
        brain_status = "BRAIN:ON" if CONFIG.brain.BRAIN_ENABLED else "BRAIN:OFF"

        # Experiment status
        experiment_status = ""
        if CONFIG.brain.BRAIN_ENABLED and self.ai_brain.experiment_active:
            remaining = CONFIG.brain.EXPERIMENT_DURATION - (
                time.time() - self.ai_brain.experiment_start_time
            )
            experiment_status = f" | EXP:{remaining:.0f}s"

        ui_lines.append(
            f"Status: {status} | Algorithm: {self.current_algorithm} | {brain_status}{experiment_status}"
        )

        # Stats
        stats = self.evaluator.get_current_stats()
        algo_stats = stats["algorithms"].get(self.current_algorithm, {})
        if algo_stats:
            ui_lines.append(
                f"Accuracy: {algo_stats.get('accuracy', 0):.1f}% ({algo_stats.get('hits', 0)}/{algo_stats.get('shots_fired', 0)})"
            )

        # Experiment history (if available)
        if CONFIG.brain.BRAIN_ENABLED and len(self.ai_brain.experiment_history) > 0:
            recent_exp = self.ai_brain.experiment_history[-1]
            ui_lines.append(
                f"Last Exp: {recent_exp.hits}/{recent_exp.shots_fired} hits ({recent_exp.accuracy:.1f}%) KPI:{recent_exp.kpi_score:.2f}"
            )

        # Controls
        ui_lines.extend(
            [
                "",
                "Controls:",
                "Click - Point camera | SPACE - Pause | D - Debug",
                "M - Manual mode | A - Auto fire | F - Fire (manual)",
                "B - Toggle AI Brain | 1/2/3 - Switch algorithm",
                "ESC - Exit",
            ]
        )

        # Render lines
        y_offset = self.window_size[1] - len(ui_lines) * 25 - 10
        for line in ui_lines:
            if line:
                text = font.render(line, True, (255, 255, 255))
                text_rect = text.get_rect()

                # Background
                bg_rect = pygame.Rect(
                    10 - 5, y_offset - 2, text_rect.width + 10, text_rect.height + 4
                )
                pygame.draw.rect(self.screen, (0, 0, 0), bg_rect)
                pygame.draw.rect(self.screen, (255, 255, 255), bg_rect, 1)

                self.screen.blit(text, (10, y_offset))
            y_offset += 25

    def run(self):
        """Main game loop"""
        log.info("Starting simulation...")

        # Start AI Brain if enabled
        if CONFIG.brain.BRAIN_ENABLED:
            self.ai_brain.start()

        while self.running:
            dt = self.clock.tick(CONFIG.environment.FPS) / 1000.0  # Convert to seconds

            self.handle_events()
            self.update(dt)
            self.render()

        # Cleanup
        self.cleanup()

    def cleanup(self):
        """Clean up and save results"""
        log.info("Shutting down...")

        # Stop AI Brain
        self.ai_brain.stop()

        # Generate and save report
        report = self.evaluator.generate_report()
        print("\n" + report)

        # Save metrics
        self.evaluator.save_metrics()
        self.evaluator.plot_performance()

        pygame.quit()
        sys.exit()


def main():
    """Entry point"""
    simulator = TurretSimulator()
    simulator.run()


if __name__ == "__main__":
    main()
