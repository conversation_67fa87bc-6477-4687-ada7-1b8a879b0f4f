#!/usr/bin/env python3
"""Test script to verify loguru colors are working"""

# Initialize logging FIRST before any other imports that might use loguru
import sys
from loguru import logger as log

# Configure logger immediately
log.remove()  # Remove default handler
log.add(
    sys.stderr,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level="DEBUG",
    colorize=True,
)

print("Testing loguru colors...")
log.debug("This is a DEBUG message - should be in color")
log.info("This is an INFO message - should be in color")
log.warning("This is a WARNING message - should be in color")
log.error("This is an ERROR message - should be in color")
log.critical("This is a CRITICAL message - should be in color")
print("Test complete!")
