"""Headless Turret Simulator for Parallel Experiments"""

import time
import numpy as np
from typing import Dict, Any
from dataclasses import dataclass
from loguru import logger as log

from config import CONFIG
from camera import Camera
from entities import EntityManager, <PERSON>eon
from targeting import TargetingSystem
from evaluation import PerformanceEvaluator, ShotMetrics


@dataclass
class HeadlessConfig:
    """Configuration for headless simulation"""

    duration: float = 30.0  # Experiment duration in seconds
    target_fps: int = 60  # Simulation FPS
    log_level: str = "WARNING"  # Reduce logging noise


@dataclass
class ExperimentResult:
    """Result from a headless experiment"""

    config_id: str
    duration: float
    shots_fired: int
    hits: int
    accuracy: float
    hits_per_minute: float
    escaped_targets: int
    kpi_score: float
    config_params: Dict[str, Any]
    final_stats: Dict[str, Any]


class HeadlessTurretSimulator:
    """Headless version of turret simulator for parallel experiments"""

    def __init__(
        self, experiment_config: Dict[str, Any], headless_config: HeadlessConfig = None
    ):
        # Set up minimal logging
        if headless_config is None:
            headless_config = HeadlessConfig()

        # Store configs
        self.experiment_config = experiment_config
        self.headless_config = headless_config

        # Configure logging for headless mode
        log.remove()
        if headless_config.log_level != "SILENT":
            log.add(lambda msg: print(msg), level=headless_config.log_level)

        # Apply experiment configuration to global CONFIG
        self._apply_experiment_config()

        # Initialize components (no pygame)
        self.window_size = (
            CONFIG.environment.WINDOW_WIDTH,
            CONFIG.environment.WINDOW_HEIGHT,
        )

        # Camera without pygame dependencies
        self.camera = Camera(self.window_size, CONFIG.environment.CAMERA_FOV)
        self.camera.max_speed = CONFIG.environment.TURRET_MAX_SPEED
        self.camera.acceleration = CONFIG.environment.TURRET_ACCELERATION

        # Core simulation components
        self.entity_manager = EntityManager()
        self.targeting_system = TargetingSystem(CONFIG.targeting.TARGETING_MODE)
        self.evaluator = PerformanceEvaluator()

        # Simulation state
        self.running = True
        self.start_time = 0.0
        self.current_time = 0.0

        # Performance tracking
        self.initial_stats = None

    def _apply_experiment_config(self):
        """Apply experiment configuration to global CONFIG"""
        for param_name, value in self.experiment_config.items():
            if hasattr(CONFIG.targeting, param_name.upper()):
                setattr(CONFIG.targeting, param_name.upper(), value)
            elif hasattr(CONFIG.environment, param_name.upper()):
                setattr(CONFIG.environment, param_name.upper(), value)

    def run_experiment(self) -> ExperimentResult:
        """Run a complete headless experiment"""
        self.start_time = time.time()
        self.current_time = 0.0

        # Record initial state
        self.initial_stats = self._get_current_stats()

        # Main simulation loop
        target_dt = 1.0 / self.headless_config.target_fps
        last_update = time.time()

        while self.current_time < self.headless_config.duration:
            real_time = time.time()
            dt = min(
                real_time - last_update, target_dt * 2
            )  # Cap dt to prevent large jumps
            last_update = real_time

            self.current_time = real_time - self.start_time

            # Update simulation
            self._update(dt)

            # Sleep to maintain target FPS (roughly)
            sleep_time = target_dt - (time.time() - real_time)
            if sleep_time > 0:
                time.sleep(sleep_time)

        # Calculate results
        return self._calculate_results()

    def _update(self, dt: float):
        """Update simulation state (headless)"""
        # Update camera
        self.camera.update(dt)

        # Update entities
        self.entity_manager.update(dt, self.current_time, CONFIG)

        # Track new pigeons
        for pigeon in self.entity_manager.pigeons:
            if pigeon.id not in self.evaluator.current_targets:
                self.evaluator.record_pigeon_spawn(pigeon.id, self.current_time)

        # Auto-targeting and firing
        decision = self.targeting_system.select_target(self.entity_manager, self.camera)

        if decision:
            # Set camera target
            self.camera.set_target_angles(decision.target_yaw, decision.target_pitch)

            # Calculate dynamic angle threshold
            if decision.target_pigeon:
                _, _, distance = decision.target_pigeon.to_spherical()
                if distance < 600:
                    angle_threshold = CONFIG.targeting.ANGLE_THRESHOLD_CLOSE
                else:
                    t = min((distance - 600) / 600, 1.0)
                    angle_threshold = CONFIG.targeting.ANGLE_THRESHOLD_CLOSE + t * (
                        CONFIG.targeting.ANGLE_THRESHOLD_FAR
                        - CONFIG.targeting.ANGLE_THRESHOLD_CLOSE
                    )
            else:
                angle_threshold = CONFIG.targeting.ANGLE_THRESHOLD_FAR

            # Fire if ready and aimed
            angle_diff = abs(self.camera.state.yaw - decision.target_yaw) + abs(
                self.camera.state.pitch - decision.target_pitch
            )

            # Adaptive fire cooldown
            if distance < CONFIG.targeting.CLOSE_TARGET_DISTANCE:
                fire_cooldown = CONFIG.targeting.FIRE_COOLDOWN_CLOSE
            else:
                fire_cooldown = CONFIG.targeting.FIRE_COOLDOWN

            if angle_diff < angle_threshold and self.targeting_system.can_fire(
                self.current_time, fire_cooldown
            ):
                self._fire_at_target(decision.target_pigeon)

    def _fire_at_target(self, pigeon: Pigeon):
        """Fire projectile at target pigeon (headless)"""
        # Record shot attempt
        self.entity_manager.record_shot_at_pigeon(pigeon.id)

        projectile = self.entity_manager.fire_projectile(
            self.current_time, pigeon, CONFIG, mode=CONFIG.targeting.TARGETING_MODE
        )

        if projectile:
            self.targeting_system.fire(self.current_time)

            # Calculate metrics
            yaw, pitch, distance = pigeon.to_spherical()
            movement_time = self.camera.get_movement_time(yaw, pitch)
            projectile_time = distance / CONFIG.targeting.PROJECTILE_SPEED

            # Create shot metrics
            shot_metrics = ShotMetrics(
                shot_time=self.current_time,
                target_id=pigeon.id,
                algorithm=CONFIG.targeting.TARGETING_MODE,
                movement_time=movement_time,
                projectile_flight_time=projectile_time,
                hit=False,  # Will be updated on collision
                distance=distance,
                target_speed=np.linalg.norm(pigeon.velocity),
                angular_velocity=0.0,
            )

            self.evaluator.record_shot(shot_metrics)

            # Store reference for hit detection
            projectile._target_pigeon_id = pigeon.id
            projectile._evaluator = self.evaluator
            projectile._algorithm = CONFIG.targeting.TARGETING_MODE

    def _get_current_stats(self) -> Dict[str, Any]:
        """Get current simulation statistics"""
        stats = self.evaluator.get_current_stats()
        brain_metrics = self.entity_manager.get_brain_metrics()

        return {
            "evaluator_stats": stats,
            "brain_metrics": brain_metrics,
            "simulation_time": self.current_time,
        }

    def _calculate_results(self) -> ExperimentResult:
        """Calculate experiment results"""
        final_stats = self._get_current_stats()

        # Use final stats directly since we reset the evaluator
        final_session = final_stats["evaluator_stats"]["session"]
        final_brain = final_stats["brain_metrics"]

        shots_fired = final_session.get("total_shots_fired", 0)
        hits = final_session.get("total_pigeons_hit", 0)
        escaped_targets = final_brain.get("escaped_targets", 0)

        # Calculate metrics
        accuracy = (hits / max(shots_fired, 1)) * 100
        hits_per_minute = (hits / max(self.current_time, 1)) * 60

        # Calculate KPI score
        kpi_score = self._calculate_kpi_score(shots_fired, hits, escaped_targets)

        return ExperimentResult(
            config_id=f"exp_{int(time.time())}",
            duration=self.current_time,
            shots_fired=shots_fired,
            hits=hits,
            accuracy=accuracy,
            hits_per_minute=hits_per_minute,
            escaped_targets=escaped_targets,
            kpi_score=kpi_score,
            config_params=self.experiment_config.copy(),
            final_stats=final_stats,
        )

    def _calculate_kpi_score(self, shots_fired: int, hits: int, escaped: int) -> float:
        """Calculate KPI score for experiment"""
        if shots_fired == 0:
            return 0.0

        accuracy = hits / shots_fired
        efficiency = hits / max(shots_fired, 1)
        hits_per_minute = (hits / max(self.current_time, 1)) * 60

        # Normalize hits per minute (assume 10 hits/min is excellent)
        normalized_hpm = min(hits_per_minute / 10.0, 1.0)

        # Penalty for escaped targets
        escape_penalty = min(escaped * 0.1, 0.5)  # Cap penalty at 0.5

        kpi_score = (
            CONFIG.brain.KPI_WEIGHTS.accuracy * accuracy
            + CONFIG.brain.KPI_WEIGHTS.hits_per_minute * normalized_hpm
            + CONFIG.brain.KPI_WEIGHTS.efficiency * efficiency
            - escape_penalty
        )

        return max(0.0, kpi_score)


def run_single_experiment(
    config: Dict[str, Any], duration: float = 30.0
) -> ExperimentResult:
    """Run a single headless experiment with given configuration"""
    headless_config = HeadlessConfig(duration=duration, log_level="SILENT")
    simulator = HeadlessTurretSimulator(config, headless_config)
    return simulator.run_experiment()


if __name__ == "__main__":
    # Test headless simulation
    test_config = {
        "fire_cooldown": 0.25,
        "fire_cooldown_close": 0.15,
        "angle_threshold_close": 2.5,
        "angle_threshold_far": 7.0,
        "max_engagement_distance": 1100.0,
    }

    print("Running test headless experiment...")
    result = run_single_experiment(test_config, duration=10.0)
    print(
        f"Result: {result.hits}/{result.shots_fired} hits ({result.accuracy:.1f}%) KPI: {result.kpi_score:.3f}"
    )
